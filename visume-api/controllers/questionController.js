const { generateSingleQuestion } = require("../utils/helpers");
const {
  getRequirementsForProfile,
  checkCompletionStatus,
  updateRequirementsInDatabase,
  getNextRequirementToTarget,
  incrementRequirementAttempt,
  checkEarlyCompletionStatus,
  selectDynamicTerminationPoint,
  getRequirementsForExtendedAttempts,
  resetAttemptsForMixedStatus
} = require("../utils/requirementsAssessment");

exports.generateNextQuestion = async (req, res) => {
  const MIN_QUESTIONS = 5;
  
  try {
    const { role, skills, previousQuestions, companyType, experience, videoProfileId } = req.body;

    // Validate required fields
    if (!role) {
      return res.status(400).json({
        success: false,
        message: "Role is required for question generation"
      });
    }

    // Parse and validate previous questions array
    // Frontend sends previousQuestions as an array of objects, each containing question and answer
    const previousQA = (previousQuestions || []).map((q) => ({
      question: q.question,
      answer: q.answer || null, // Directly use the answer from the question object
      type: q.type,
      question_number: q.question_number,
      total_questions: q.total_questions
    }));

    console.log("Previous Questions:", previousQA);

    // Always enforce strict maximum of 10 questions per session
    const totalQuestions = 10;
    const questionCount = (previousQuestions || []).length + 1;

    // Check if we've reached the total questions limit
    if (questionCount > totalQuestions) {
      return res.status(200).json({
        success: false,
        message: "Interview completed",
        completed: true
      });
    }

    console.log(`Generating question ${questionCount} of ${totalQuestions}`);

    // 🎯 REQUIREMENTS-BASED COMPLETION CHECK
    let requirements = null;
    let completionStatus = null;
    let targetRequirement = null;

    if (videoProfileId) {
      try {
        console.log(`🔍 FETCHING REQUIREMENTS for video profile: ${videoProfileId}`);
        requirements = await getRequirementsForProfile(videoProfileId);

        if (requirements) {
          // 🎯 NEW: Check for early completion and dynamic termination
          const earlyCompletionStatus = checkEarlyCompletionStatus(requirements, questionCount);

          // Check if we have a stored dynamic termination point
          let dynamicTerminationPoint = requirements.dynamicTerminationPoint;

          if (earlyCompletionStatus.needsDynamicTermination && !dynamicTerminationPoint) {
            console.log(`🚀 EARLY COMPLETION DETECTED - SELECTING DYNAMIC TERMINATION POINT:`);
            console.log(`   - Current question: ${questionCount}/${totalQuestions}`);
            console.log(`   - Reason: ${earlyCompletionStatus.reason}`);
            console.log(`   - Termination type: ${earlyCompletionStatus.terminationType}`);

            try {
              // Generate AI-selected termination point
              const terminationInfo = await selectDynamicTerminationPoint(
                requirements,
                previousQA,
                role,
                skills
              );

              dynamicTerminationPoint = terminationInfo.selectedTerminationPoint;

              // Store the termination point in requirements for persistence
              requirements.dynamicTerminationPoint = dynamicTerminationPoint;
              requirements.dynamicTerminationInfo = terminationInfo;

              // Update requirements in database
              await updateRequirementsInDatabase(videoProfileId, requirements);

              console.log(`✅ DYNAMIC TERMINATION POINT SELECTED AND SAVED:`);
              console.log(`   - Selected termination point: Question ${dynamicTerminationPoint}`);
              console.log(`   - Reasoning: ${terminationInfo.reasoning}`);
              console.log(`   - Confidence: ${terminationInfo.confidenceLevel}`);
              console.log(`   - Method: ${terminationInfo.selectionMethod}`);

            } catch (terminationError) {
              console.error("❌ Failed to select dynamic termination point:", terminationError);
              // Continue with normal flow if termination selection fails
            }
          }

          // 🎯 NEW: Handle mixed requirements status (some satisfied, others exhausted)
          if (earlyCompletionStatus.terminationType === 'extended') {
            console.log(`🔄 MIXED REQUIREMENTS STATUS DETECTED:`);
            console.log(`   - Some requirements satisfied, others exhausted`);
            console.log(`   - Implementing extended attempts strategy`);

            try {
              // Get requirements that could benefit from extended attempts
              const extendedAttemptCandidates = getRequirementsForExtendedAttempts(requirements, questionCount);

              if (extendedAttemptCandidates.length > 0) {
                // Reset attempts for promising requirements
                requirements = resetAttemptsForMixedStatus(requirements, extendedAttemptCandidates);

                // Update requirements in database
                await updateRequirementsInDatabase(videoProfileId, requirements);

                console.log(`✅ EXTENDED ATTEMPTS ACTIVATED:`);
                console.log(`   - Reset attempts for ${extendedAttemptCandidates.length} requirements`);
                console.log(`   - Giving candidates more chances to satisfy requirements`);

                // Continue with question generation targeting these requirements
              } else {
                console.log(`⚠️ No suitable candidates for extended attempts found`);
              }
            } catch (extendedAttemptsError) {
              console.error("❌ Failed to implement extended attempts:", extendedAttemptsError);
              // Continue with normal flow if extended attempts setup fails
            }
          }

          // Check if we've reached the dynamic termination point
          // Note: Terminate AFTER the selected question, not AT it
          if (dynamicTerminationPoint && questionCount > dynamicTerminationPoint) {
            console.log(`🎯 DYNAMIC TERMINATION POINT REACHED:`);
            console.log(`   - Termination point: After question ${dynamicTerminationPoint}`);
            console.log(`   - Current question: ${questionCount}`);
            console.log(`   - Reason: ${requirements.dynamicTerminationInfo?.reasoning || 'Dynamic termination'}`);

            return res.status(200).json({
              success: true,
              message: `Interview completed - Dynamic termination after question ${dynamicTerminationPoint}`,
              completed: true,
              dynamicTermination: true,
              terminationPoint: dynamicTerminationPoint,
              terminationInfo: requirements.dynamicTerminationInfo || null
            });
          }

          // Check completion status with minimum attempts option
          const minAttemptsPerRequirement = 1; // Configurable: minimum attempts before auto-completion
          completionStatus = checkCompletionStatus(requirements, { minAttemptsPerRequirement });

          // Enhanced auto-completion logic:
          // Only auto-complete if we've reached the maximum question limit (10 questions)
          // This allows candidates to continue even after requirements are satisfied
          if (questionCount >= totalQuestions && completionStatus.canAutoComplete) {
            console.log(`✅ MAXIMUM QUESTIONS REACHED - AUTO-COMPLETION:`);
            console.log(`   - Standard completion: ${completionStatus.satisfiedCount}/${completionStatus.totalRequired} satisfied (${completionStatus.completionPercentage}%)`);
            console.log(`   - Cannot fulfill: ${completionStatus.cannotFulfillCount}/${completionStatus.totalRequired} requirements`);
            console.log(`   - Effective completion: ${completionStatus.effectiveCompletionPercentage}%`);
            console.log(`   - Questions completed: ${questionCount}/${totalQuestions}`);

            return res.status(200).json({
              success: true,
              message: "Interview completed - Maximum questions reached",
              completed: true,
              autoCompleted: true,
              requirementsStatus: completionStatus
            });
          }

          // Log requirements status but continue generating questions
          if (questionCount >= MIN_QUESTIONS && completionStatus.canAutoComplete) {
            console.log(`📊 REQUIREMENTS SATISFIED - BUT CONTINUING:`);
            console.log(`   - Requirements completion: ${completionStatus.completionPercentage}%`);
            console.log(`   - Candidate can continue with additional questions`);
            if (dynamicTerminationPoint) {
              console.log(`   - Will terminate at question ${dynamicTerminationPoint} (dynamic)`);
            } else {
              console.log(`   - Will auto-complete at question ${totalQuestions}`);
            }
          }

          // Get the next requirement to target for question generation
          targetRequirement = getNextRequirementToTarget(requirements);

          console.log(`📊 REQUIREMENTS STATUS:`, {
            satisfied: `${completionStatus.satisfiedCount}/${completionStatus.totalRequired}`,
            cannotFulfill: `${completionStatus.cannotFulfillCount}/${completionStatus.totalRequired}`,
            effectiveCompletion: `${completionStatus.effectiveCompletionPercentage}%`,
            remainingViableRequirements: completionStatus.unsatisfiedRequirements.length
          });

          if (targetRequirement) {
            console.log(`🎯 TARGETING REQUIREMENT: ${targetRequirement.id} - ${targetRequirement.parameter || targetRequirement.description?.substring(0, 80)}...`);
            console.log(`   - Category: ${targetRequirement.category}`);
            console.log(`   - Current score: ${targetRequirement.satisfactionScore || 0}/10`);
            console.log(`   - Attempt: ${(targetRequirement.attemptCount || 0) + 1} of ${targetRequirement.maxAttempts || 2}`);
          } else {
            console.log(`⚠️ NO VIABLE REQUIREMENTS TO TARGET - Will generate follow-up question based on previous responses`);
          }
        }
      } catch (reqError) {
        console.error("❌ Requirements check failed, continuing with standard flow:", reqError);
        // Continue with standard question generation if requirements check fails
      }
    }

    // Enforce alternation/shuffling between behavioral and technical questions
    const isFirstQuestion = !previousQuestions || previousQuestions.length === 0;

    // 🎯 REQUIREMENTS-ENHANCED TYPE DETERMINATION
    let forcedType = null;

    if (isFirstQuestion) {
      forcedType = "behavioral";
      console.log(`🎬 FIRST QUESTION: Starting with behavioral question`);
    } else {
      // Default alternation logic
      const prevTypes = previousQA.map(q => q.type);
      const lastType = prevTypes[prevTypes.length - 1];

      // Enhanced requirements-based type determination
      if (targetRequirement) {
        // Determine question type based on targeted requirement
        const category = targetRequirement.category.toLowerCase();

        // Map requirement categories to question types
        if (category.includes('technical') || category.includes('problem_solving')) {
          forcedType = "technical";
        } else if (category.includes('communication') || category.includes('behavioral')) {
          forcedType = "behavioral";
        } else {
          // For other categories, alternate but with bias toward technical
          forcedType = lastType === "technical" ? "behavioral" : "technical";
        }

        console.log(`🎯 TARGETED QUESTION TYPE: ${forcedType} based on requirement ${targetRequirement.id}`);
      } else {
        // No target requirement - either all satisfied or no requirements available
        // Generate follow-up questions based on previous responses
        if (completionStatus && completionStatus.canAutoComplete && questionCount >= MIN_QUESTIONS) {
          // Requirements are satisfied, generate follow-up questions
          console.log(`🔄 REQUIREMENTS SATISFIED - GENERATING FOLLOW-UP QUESTIONS`);
          forcedType = lastType === "behavioral" ? "technical" : "behavioral";
        } else if (completionStatus && completionStatus.nextFocusArea) {
          const focusArea = completionStatus.nextFocusArea.toLowerCase();
          if (focusArea.includes('technical') || focusArea.includes('problem')) {
            forcedType = "technical";
          } else if (focusArea.includes('communication') || focusArea.includes('behavioral')) {
            forcedType = "behavioral";
          } else {
            // Alternate with bias toward technical for better assessment
            forcedType = lastType === "technical" ? "behavioral" : "technical";
          }
        } else {
          // Standard alternation if no requirements guidance
          forcedType = lastType === "behavioral" ? "technical" : "behavioral";
        }

        console.log(`🔄 STANDARD QUESTION TYPE: ${forcedType} (last: ${lastType})`);
      }
    }

    const nextQuestion = await generateSingleQuestion(
      role,
      previousQA,
      skills,
      isFirstQuestion,
      companyType,
      experience,
      forcedType,
      targetRequirement
    );

    // 🎯 INCREMENT ATTEMPT COUNT if question targets a specific requirement
    if (targetRequirement && requirements && videoProfileId) {
      try {
        console.log(`🔄 INCREMENTING ATTEMPT COUNT for targeted requirement: ${targetRequirement.id}`);
        const updatedRequirements = incrementRequirementAttempt(requirements, targetRequirement.id);
        await updateRequirementsInDatabase(videoProfileId, updatedRequirements);
        console.log(`✅ Attempt count incremented and saved for requirement: ${targetRequirement.id}`);
      } catch (attemptError) {
        console.error("❌ Failed to increment attempt count:", attemptError);
        // Continue with question generation even if attempt increment fails
      }
    }

    // Validate and ensure timerDuration is within acceptable range
    const timerDuration = Math.min(90, Math.max(30, parseInt(nextQuestion.timerDuration) || 90));

    const response = {
      success: true,
      question: nextQuestion.question,
      type: nextQuestion.type,
      timerDuration: timerDuration,
      question_number: questionCount,
      total_questions: totalQuestions,
      completed: questionCount >= totalQuestions,
      _fallback: nextQuestion._fallback || false
    };

    res.status(200).json(response);

  } catch (error) {
    console.error("Error generating next question:", error);
    
    // Simple fallback
    const fallbackQuestion = {
      // Remove fallback questions; throw error if no questions can be generated
      question: null,
      type: "behavioral",
      timerDuration: 90, // Default fallback duration
      _fallback: true
    };

    const status = error.message === "Interview completed" ? 200 : 500;
    res.status(status).json({
      success: status === 200,
      ...fallbackQuestion,
      completed: error.message === "Interview completed",
      error_details: error.message
    });
  }
};

exports.analyzeAnswer = async (req, res) => {
  try {
    const { question, answer, role, skills, previousQA, videoProfileId } = req.body;

    if (!question || !role) {
      return res.status(400).json({
        message: "Question and role are required fields"
      });
    }

    // Import helpers
    const { analyzeAnswerAndGenerateFollowUp } = require("../utils/helpers");

    console.log("🔍 ANALYZING ANSWER:", {
      question: question.substring(0, 50) + "...",
      answerLength: answer?.length,
      role,
      skillsCount: skills?.length,
      hasVideoProfileId: !!videoProfileId
    });

    // 🎯 FETCH REQUIREMENTS FOR ENHANCED ANALYSIS
    let requirements = null;
    if (videoProfileId) {
      try {
        requirements = await getRequirementsForProfile(videoProfileId);
        if (requirements) {
          console.log(`📋 REQUIREMENTS LOADED: ${requirements.requirements?.length || 0} requirements for analysis`);
        }
      } catch (reqError) {
        console.error("❌ Failed to fetch requirements for analysis:", reqError);
      }
    }

    // Get analysis and follow-up (with requirements if available)
    const analysisResult = await analyzeAnswerAndGenerateFollowUp(
      question,
      answer,
      role,
      skills,
      previousQA,
      requirements
    );

    // 💾 UPDATE REQUIREMENTS IN DATABASE if they were modified
    if (requirements && analysisResult.updatedRequirements && videoProfileId) {
      try {
        await updateRequirementsInDatabase(videoProfileId, analysisResult.updatedRequirements);
      } catch (updateError) {
        console.error("❌ Failed to update requirements in database:", updateError);
      }
    }

    console.log("✅ ANALYSIS COMPLETED:", {
      hasAnalysis: !!analysisResult.analysis,
      hasFollowUp: !!analysisResult.follow_up?.question,
      hasRequirementsStatus: !!analysisResult.requirementsStatus
    });

    // Return the enhanced analysis result with requirements status
    res.json({
      analysis: analysisResult.analysis,
      follow_up: analysisResult.follow_up,
      requirementsStatus: analysisResult.requirementsStatus || null
    });

  } catch (error) {
    console.error("Error analyzing answer:", error);
    res.status(500).json({
      message: "Failed to analyze answer",
      error: error.message,
      analysis: {
        technical_accuracy: "Analysis failed",
        communication: "Analysis failed",
        knowledge_gaps: ["Unable to analyze response"]
      },
      follow_up: {
        question: "Could you provide more details about your previous answer?",
        reasoning: "Default follow-up due to analysis error",
        expected_focus: "General elaboration"
      }
    });
  }
};
